/* ========================================
   BACKGROUND MODAL AND SELECTION
   ======================================== */

/* Background Change Container */
.background-change-container {
    background: #f8f9fa;
    border-radius: var(--border-radius);
    padding: var(--spacing-md);
    border: 1px solid #e0e0e0;
}

.background-controls {
    text-align: center;
}

/* Background Modal Styles */
.background-modal {
    position: fixed;
    top: 0;
    left: 0;
    width: 100%;
    height: 100%;
    z-index: 10000;
    display: flex;
    align-items: center;
    justify-content: center;
}

.modal-overlay {
    position: absolute;
    top: 0;
    left: 0;
    width: 100%;
    height: 100%;
    background: rgba(108, 117, 125, 0.5);
}

.modal-content {
    position: relative;
    background: white;
    border-radius: var(--border-radius);
    max-width: 90vw;
    max-height: 80vh;
    overflow: hidden;
    box-shadow: 0 10px 30px rgba(108, 117, 125, 0.3);
}

.modal-header {
    display: flex;
    justify-content: space-between;
    align-items: center;
    padding: var(--spacing-md);
    border-bottom: 1px solid #e0e0e0;
    background: #f8f9fa;
}

.modal-header h3 {
    margin: 0;
    color: #495057;
}

.modal-body {
    padding: var(--spacing-md);
    max-height: 60vh;
    overflow-y: auto;
}

.background-grid {
    display: grid;
    grid-template-columns: repeat(auto-fill, minmax(120px, 1fr));
    gap: var(--spacing-sm);
}

.background-option {
    aspect-ratio: 1;
    background-size: cover;
    background-position: center;
    border: 2px solid #e0e0e0;
    border-radius: 4px;
    cursor: pointer;
    transition: all 0.2s;
}

.background-option:hover {
    border-color: var(--primary-color);
    transform: scale(1.05);
}

.close-btn {
    background: none;
    border: none;
    font-size: 24px;
    cursor: pointer;
    color: #6c757d;
    padding: 4px;
    line-height: 1;
}

.close-btn:hover {
    color: #495057;
}
